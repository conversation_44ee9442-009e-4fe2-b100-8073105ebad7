// background.js: Handles long-running, tab-specific summarization tasks.

// --- State Management ---

/**
 * Updates the state for a specific tab in chrome.storage.local.
 * @param {number} tabId The ID of the tab to update.
 * @param {object} partialState An object with the state properties to update.
 */
async function updateState(tabId, partialState) {
    const key = `tab_${tabId}`;
    try {
        const data = await chrome.storage.local.get(key);
        const currentState = data[key] || {};
        // Merge new state into the current state
        const newState = {...currentState, ...partialState};
        await chrome.storage.local.set({[key]: newState});
    } catch (error) {
        console.error(`Error updating state for tab ${tabId}:`, error);
    }
}

// --- Event Listeners ---

// Listen for a message from the popup to start summarization.
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    // Ensure the message is from a tab and contains the necessary info.
    if (request.action === 'summarize' && sender.tab && sender.tab.id) {
        const {id: tabId, url: tabUrl} = sender.tab;

        // Start the async summarization process for the specific tab.
        summarize(tabId, tabUrl, request.text, request.settings);

        // Immediately respond to the popup to confirm the task has been received.
        sendResponse({status: 'Processing started in the background.'});
    }
    // Keep the message channel open for async operations.
    return true;
});

// Clean up storage when a tab is closed to prevent memory leaks.
chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
    const key = `tab_${tabId}`;
    chrome.storage.local.remove(key, () => {
        if (chrome.runtime.lastError) {
            console.error(`Error removing storage for closed tab ${tabId}:`, chrome.runtime.lastError);
        } else {
            console.log(`Cleaned up storage for closed tab: ${tabId}`);
        }
    });
});


// --- Core Summarization Logic ---

async function summarize(tabId, url, pageText, settings) {
    // Set initial state for processing for this specific tab.
    await updateState(tabId, {
        url: url,
        isProcessing: true,
        summary: '',
        status: 'Initializing...',
        isError: false,
        settings: settings // Save settings used for this summary
    });

    try {
        if (!('Summarizer' in self) || (await Summarizer.availability()) === 'unavailable') {
            throw new Error('Summarizer API is not available. It is available only in Chrome 138 and later.');
        }

        await updateState(tabId, {status: 'Initializing summarizer...'});
        const summarizer = await Summarizer.create({
            ...settings,
            format: "plain-text",
            monitor(monitor) {
                monitor.addEventListener("downloadprogress", (e) => {
                    const progress = (e.loaded * 100).toFixed(0);
                    updateState(tabId, {status: `Downloading model: ${progress}%`});
                });
            },
        });

        await updateState(tabId, {status: 'Generating summary...'});
        const stream = await summarizer.summarizeStreaming(pageText);
        let currentSummary = "";
        for await (const chunk of stream) {
            currentSummary += chunk;
            // Update storage with the partial summary for live updates in the popup.
            await updateState(tabId, {summary: currentSummary});
        }

        if (currentSummary) {
            await updateState(tabId, {
                status: 'Summary generated successfully!',
                isError: false,
                isProcessing: false,
            });
        } else {
            throw new Error('Could not generate a summary.');
        }
    } catch (error) {
        console.error(`Error in background summarization for tab ${tabId}:`, error);
        await updateState(tabId, {
            status: `An error occurred: ${error.message}`,
            summary: 'Failed to generate summary. Please try again.',
            isError: true,
            isProcessing: false,
        });
    }
}