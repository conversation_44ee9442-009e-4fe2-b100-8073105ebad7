// state-manager.js: Advanced state management utilities for the News Summarizer extension

/**
 * Enhanced state management class for the News Summarizer extension
 */
class StateManager {
    constructor() {
        this.GLOBAL_SETTINGS_KEY = 'global_user_preferences';
        this.STATE_VERSION = '1.0';
        this.MAX_STATE_AGE_DAYS = 7;
    }

    /**
     * Gets the storage key for a specific tab
     * @param {number} tabId - The tab ID
     * @returns {string} The storage key
     */
    getTabKey(tabId) {
        return `tab_${tabId}`;
    }

    /**
     * Validates state object structure
     * @param {object} state - The state object to validate
     * @returns {boolean} Whether the state is valid
     */
    validateState(state) {
        if (!state || typeof state !== 'object') return false;
        
        // Check for required properties and types
        const validProperties = {
            url: 'string',
            isProcessing: 'boolean',
            summary: 'string',
            status: 'string',
            isError: 'boolean',
            settings: 'object',
            lastUpdated: 'number'
        };

        for (const [prop, expectedType] of Object.entries(validProperties)) {
            if (state.hasOwnProperty(prop) && typeof state[prop] !== expectedType) {
                console.warn(`Invalid state property ${prop}: expected ${expectedType}, got ${typeof state[prop]}`);
                return false;
            }
        }

        return true;
    }

    /**
     * Safely updates tab state with validation and error recovery
     * @param {number} tabId - The tab ID
     * @param {object} partialState - The state updates to apply
     * @returns {Promise<boolean>} Success status
     */
    async updateTabState(tabId, partialState) {
        const key = this.getTabKey(tabId);
        
        try {
            // Get current state
            const data = await chrome.storage.local.get(key);
            const currentState = data[key] || {};
            
            // Merge and validate new state
            const newState = {
                ...currentState,
                ...partialState,
                lastUpdated: Date.now(),
                version: this.STATE_VERSION
            };

            if (!this.validateState(newState)) {
                throw new Error('Invalid state structure');
            }

            await chrome.storage.local.set({[key]: newState});
            return true;
        } catch (error) {
            console.error(`Error updating state for tab ${tabId}:`, error);
            return await this.createRecoveryState(tabId, partialState);
        }
    }

    /**
     * Creates a recovery state when normal state update fails
     * @param {number} tabId - The tab ID
     * @param {object} partialState - The state to recover
     * @returns {Promise<boolean>} Success status
     */
    async createRecoveryState(tabId, partialState) {
        try {
            const recoveryState = {
                ...partialState,
                lastUpdated: Date.now(),
                version: this.STATE_VERSION,
                isRecovered: true,
                recoveryTimestamp: Date.now()
            };

            await chrome.storage.local.set({[this.getTabKey(tabId)]: recoveryState});
            console.log(`Recovery state created for tab ${tabId}`);
            return true;
        } catch (recoveryError) {
            console.error(`Failed to create recovery state for tab ${tabId}:`, recoveryError);
            return false;
        }
    }

    /**
     * Gets tab state with fallback handling
     * @param {number} tabId - The tab ID
     * @returns {Promise<object>} The tab state or empty object
     */
    async getTabState(tabId) {
        try {
            const key = this.getTabKey(tabId);
            const data = await chrome.storage.local.get(key);
            const state = data[key] || {};
            
            if (state && !this.validateState(state)) {
                console.warn(`Invalid state found for tab ${tabId}, returning empty state`);
                return {};
            }
            
            return state;
        } catch (error) {
            console.error(`Error getting state for tab ${tabId}:`, error);
            return {};
        }
    }

    /**
     * Saves global user preferences
     * @param {object} preferences - The preferences to save
     * @returns {Promise<boolean>} Success status
     */
    async saveGlobalPreferences(preferences) {
        try {
            const prefData = {
                ...preferences,
                lastUpdated: Date.now(),
                version: this.STATE_VERSION
            };
            
            await chrome.storage.local.set({[this.GLOBAL_SETTINGS_KEY]: prefData});
            return true;
        } catch (error) {
            console.error('Error saving global preferences:', error);
            return false;
        }
    }

    /**
     * Loads global user preferences
     * @returns {Promise<object>} The preferences or defaults
     */
    async loadGlobalPreferences() {
        try {
            const data = await chrome.storage.local.get(this.GLOBAL_SETTINGS_KEY);
            return data[this.GLOBAL_SETTINGS_KEY] || {
                summaryType: 'key-points',
                summaryLength: 'medium'
            };
        } catch (error) {
            console.error('Error loading global preferences:', error);
            return {
                summaryType: 'key-points',
                summaryLength: 'medium'
            };
        }
    }

    /**
     * Performs comprehensive cleanup of old states
     * @returns {Promise<number>} Number of states cleaned up
     */
    async performCleanup() {
        try {
            const allData = await chrome.storage.local.get(null);
            const now = Date.now();
            const maxAge = this.MAX_STATE_AGE_DAYS * 24 * 60 * 60 * 1000;
            const keysToRemove = [];

            for (const [key, value] of Object.entries(allData)) {
                // Only process tab-specific keys
                if (key.startsWith('tab_') && value && typeof value === 'object') {
                    const age = now - (value.lastUpdated || 0);
                    if (age > maxAge) {
                        keysToRemove.push(key);
                    }
                }
            }

            if (keysToRemove.length > 0) {
                await chrome.storage.local.remove(keysToRemove);
                console.log(`Cleaned up ${keysToRemove.length} old tab states`);
            }

            return keysToRemove.length;
        } catch (error) {
            console.error('Error during state cleanup:', error);
            return 0;
        }
    }

    /**
     * Exports all extension state for backup
     * @returns {Promise<object>} The exported state data
     */
    async exportState() {
        try {
            const allData = await chrome.storage.local.get(null);
            return {
                exportedAt: Date.now(),
                version: this.STATE_VERSION,
                data: allData
            };
        } catch (error) {
            console.error('Error exporting state:', error);
            return null;
        }
    }

    /**
     * Gets storage usage statistics
     * @returns {Promise<object>} Storage usage information
     */
    async getStorageStats() {
        try {
            const allData = await chrome.storage.local.get(null);
            const stats = {
                totalKeys: 0,
                tabStates: 0,
                globalSettings: 0,
                oldStates: 0,
                totalSize: 0
            };

            const now = Date.now();
            const maxAge = this.MAX_STATE_AGE_DAYS * 24 * 60 * 60 * 1000;

            for (const [key, value] of Object.entries(allData)) {
                stats.totalKeys++;
                stats.totalSize += JSON.stringify(value).length;

                if (key.startsWith('tab_')) {
                    stats.tabStates++;
                    const age = now - (value.lastUpdated || 0);
                    if (age > maxAge) {
                        stats.oldStates++;
                    }
                } else if (key === this.GLOBAL_SETTINGS_KEY) {
                    stats.globalSettings++;
                }
            }

            return stats;
        } catch (error) {
            console.error('Error getting storage stats:', error);
            return null;
        }
    }
}

// Create a global instance for use across the extension
const stateManager = new StateManager();

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StateManager;
}
