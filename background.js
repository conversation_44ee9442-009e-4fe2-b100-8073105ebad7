// background.js: Handles long-running, tab-specific summarization tasks.

// --- State Management ---

/**
 * Updates the state for a specific tab in chrome.storage.local.
 * @param {number} tabId The ID of the tab to update.
 * @param {object} partialState An object with the state properties to update.
 */
async function updateState(tabId, partialState) {
    const key = `tab_${tabId}`;
    try {
        const data = await chrome.storage.local.get(key);
        const currentState = data[key] || {};
        // Merge new state into the current state
        const newState = {
            ...currentState,
            ...partialState,
            lastUpdated: Date.now() // Add timestamp for state tracking
        };
        await chrome.storage.local.set({[key]: newState});
    } catch (error) {
        console.error(`Error updating state for tab ${tabId}:`, error);
        // Attempt recovery by creating a fresh state
        try {
            const recoveryState = {
                ...partialState,
                lastUpdated: Date.now(),
                isRecovered: true
            };
            await chrome.storage.local.set({[key]: recoveryState});
            console.log(`Recovery state created for tab ${tabId}`);
        } catch (recoveryError) {
            console.error(`Failed to create recovery state for tab ${tabId}:`, recoveryError);
        }
    }
}

/**
 * Validates and cleans up old tab states to prevent storage bloat
 */
async function cleanupOldStates() {
    try {
        const allData = await chrome.storage.local.get(null);
        const now = Date.now();
        const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
        const keysToRemove = [];

        for (const [key, value] of Object.entries(allData)) {
            // Only process tab-specific keys
            if (key.startsWith('tab_') && value && typeof value === 'object') {
                const age = now - (value.lastUpdated || 0);
                if (age > maxAge) {
                    keysToRemove.push(key);
                }
            }
        }

        if (keysToRemove.length > 0) {
            await chrome.storage.local.remove(keysToRemove);
            console.log(`Cleaned up ${keysToRemove.length} old tab states`);
        }
    } catch (error) {
        console.error('Error during state cleanup:', error);
    }
}

// --- Event Listeners ---

// Listen for a message from the popup to start summarization.
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    // Ensure the message is from a tab and contains the necessary info.
    if (request.action === 'summarize' && sender.tab && sender.tab.id) {
        const {id: tabId, url: tabUrl} = sender.tab;

        // Start the async summarization process for the specific tab.
        summarize(tabId, tabUrl, request.text, request.settings);

        // Immediately respond to the popup to confirm the task has been received.
        sendResponse({status: 'Processing started in the background.'});
    }
    // Keep the message channel open for async operations.
    return true;
});

// Clean up storage when a tab is closed to prevent memory leaks.
chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
    const key = `tab_${tabId}`;
    chrome.storage.local.remove(key, () => {
        if (chrome.runtime.lastError) {
            console.error(`Error removing storage for closed tab ${tabId}:`, chrome.runtime.lastError);
        } else {
            console.log(`Cleaned up storage for closed tab: ${tabId}`);
        }
    });
});

// Periodic cleanup of old states (run every hour)
chrome.alarms.create('cleanup-old-states', { periodInMinutes: 60 });
chrome.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name === 'cleanup-old-states') {
        cleanupOldStates();
    }
});

// Run cleanup on extension startup
chrome.runtime.onStartup.addListener(() => {
    cleanupOldStates();
});

// Also run cleanup when extension is installed/updated
chrome.runtime.onInstalled.addListener(() => {
    cleanupOldStates();
});


// --- Core Summarization Logic ---

async function summarize(tabId, url, pageText, settings) {
    // Set initial state for processing for this specific tab.
    await updateState(tabId, {
        url: url,
        isProcessing: true,
        summary: '',
        status: 'Initializing...',
        isError: false,
        settings: settings, // Save settings used for this summary
        pageText: pageText.substring(0, 500), // Store first 500 chars for reference
        startTime: Date.now() // Track when summarization started
    });

    try {
        if (!('Summarizer' in self) || (await Summarizer.availability()) === 'unavailable') {
            throw new Error('Summarizer API is not available. It is available only in Chrome 138 and later.');
        }

        await updateState(tabId, {status: 'Initializing summarizer...'});
        const summarizer = await Summarizer.create({
            ...settings,
            format: "plain-text",
            monitor(monitor) {
                monitor.addEventListener("downloadprogress", (e) => {
                    const progress = (e.loaded * 100).toFixed(0);
                    updateState(tabId, {status: `Downloading model: ${progress}%`});
                });
            },
        });

        await updateState(tabId, {status: 'Generating summary...'});
        const stream = await summarizer.summarizeStreaming(pageText);
        let currentSummary = "";
        for await (const chunk of stream) {
            currentSummary += chunk;
            // Update storage with the partial summary for live updates in the popup.
            await updateState(tabId, {summary: currentSummary});
        }

        if (currentSummary) {
            const endTime = Date.now();
            const processingTime = endTime - (await chrome.storage.local.get(`tab_${tabId}`)).then(data =>
                data[`tab_${tabId}`]?.startTime || endTime
            );

            await updateState(tabId, {
                status: 'Summary generated successfully!',
                isError: false,
                isProcessing: false,
                completedAt: endTime,
                processingTimeMs: processingTime
            });
        } else {
            throw new Error('Could not generate a summary.');
        }
    } catch (error) {
        console.error(`Error in background summarization for tab ${tabId}:`, error);
        await updateState(tabId, {
            status: `An error occurred: ${error.message}`,
            summary: 'Failed to generate summary. Please try again.',
            isError: true,
            isProcessing: false,
        });
    }
}